'use client'

import { useScrollAnimation, animationPresets } from '@/hooks/useScrollAnimation'
import Circle, { SoftCircle } from './Circle'
import Rectangle, { RoundedRectangle } from './Rectangle'
import Triangle from './Triangle'
import HalfCircle from './HalfCircle'
import { SoftGrid, RoundedRect, Pill, Blob, QuarterCircle } from './RoundedShapes'

// Generate a consistent random seed based on position and type
const generateSeed = (type: string, index: number = 0) => {
  let hash = 0
  const str = `${type}-${index}`
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return Math.abs(hash) / 2147483647 // Normalize to 0-1
}

// Animated Circle Components
interface AnimatedCircleProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'red' | 'yellow' | 'blue' | 'black' | 'white'
  className?: string
  animationPreset?: keyof typeof animationPresets
  animationIndex?: number
}

export function AnimatedCircle({ 
  animationPreset = 'gentle', 
  animationIndex = 0,
  ...props 
}: AnimatedCircleProps) {
  const animation = useScrollAnimation({
    ...animationPresets[animationPreset],
    randomSeed: generateSeed('circle', animationIndex)
  })
  
  return (
    <div ref={animation.ref} style={animation.style}>
      <Circle {...props} />
    </div>
  )
}

export function AnimatedSoftCircle({ 
  animationPreset = 'subtle', 
  animationIndex = 0,
  ...props 
}: AnimatedCircleProps) {
  const animation = useScrollAnimation({
    ...animationPresets[animationPreset],
    randomSeed: generateSeed('soft-circle', animationIndex)
  })
  
  return (
    <div ref={animation.ref} style={animation.style}>
      <SoftCircle {...props} />
    </div>
  )
}

// Animated Rectangle Components
interface AnimatedRectangleProps {
  width?: 'sm' | 'md' | 'lg' | 'xl'
  height?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'red' | 'yellow' | 'blue' | 'black' | 'white'
  className?: string
  animationPreset?: keyof typeof animationPresets
  animationIndex?: number
}

export function AnimatedRectangle({ 
  animationPreset = 'gentle', 
  animationIndex = 0,
  ...props 
}: AnimatedRectangleProps) {
  const animation = useScrollAnimation({
    ...animationPresets[animationPreset],
    randomSeed: generateSeed('rectangle', animationIndex)
  })
  
  return (
    <div ref={animation.ref} style={animation.style}>
      <Rectangle {...props} />
    </div>
  )
}

export function AnimatedRoundedRectangle({ 
  animationPreset = 'gentle', 
  animationIndex = 0,
  ...props 
}: AnimatedRectangleProps) {
  const animation = useScrollAnimation({
    ...animationPresets[animationPreset],
    randomSeed: generateSeed('rounded-rectangle', animationIndex)
  })
  
  return (
    <div ref={animation.ref} style={animation.style}>
      <RoundedRectangle {...props} />
    </div>
  )
}

// Animated Triangle Component
interface AnimatedTriangleProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'red' | 'yellow' | 'blue' | 'black' | 'white'
  direction?: 'up' | 'down' | 'left' | 'right'
  className?: string
  animationPreset?: keyof typeof animationPresets
  animationIndex?: number
}

export function AnimatedTriangle({ 
  animationPreset = 'dynamic', 
  animationIndex = 0,
  ...props 
}: AnimatedTriangleProps) {
  const animation = useScrollAnimation({
    ...animationPresets[animationPreset],
    randomSeed: generateSeed('triangle', animationIndex)
  })
  
  return (
    <div ref={animation.ref} style={animation.style}>
      <Triangle {...props} />
    </div>
  )
}

// Animated HalfCircle Component
interface AnimatedHalfCircleProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'red' | 'yellow' | 'blue' | 'black' | 'white'
  direction?: 'top' | 'bottom' | 'left' | 'right'
  className?: string
  animationPreset?: keyof typeof animationPresets
  animationIndex?: number
}

export function AnimatedHalfCircle({ 
  animationPreset = 'gentle', 
  animationIndex = 0,
  ...props 
}: AnimatedHalfCircleProps) {
  const animation = useScrollAnimation({
    ...animationPresets[animationPreset],
    randomSeed: generateSeed('half-circle', animationIndex)
  })
  
  return (
    <div ref={animation.ref} style={animation.style}>
      <HalfCircle {...props} />
    </div>
  )
}

// Animated Rounded Shapes
interface AnimatedRoundedShapeProps {
  className?: string
  color?: 'red' | 'yellow' | 'blue' | 'black'
  animationPreset?: keyof typeof animationPresets
  animationIndex?: number
}

interface AnimatedSoftGridProps extends AnimatedRoundedShapeProps {
  opacity?: 'default' | 'hero'
}

export function AnimatedSoftGrid({
  animationPreset = 'subtle',
  animationIndex = 0,
  opacity = 'default',
  ...props
}: AnimatedSoftGridProps) {
  const animation = useScrollAnimation({
    ...animationPresets[animationPreset],
    randomSeed: generateSeed('soft-grid', animationIndex)
  })

  return (
    <div ref={animation.ref} className="h-full" style={animation.style}>
      <SoftGrid opacity={opacity} {...props} />
    </div>
  )
}

export function AnimatedRoundedRect({ 
  animationPreset = 'gentle', 
  animationIndex = 0,
  ...props 
}: AnimatedRoundedShapeProps) {
  const animation = useScrollAnimation({
    ...animationPresets[animationPreset],
    randomSeed: generateSeed('rounded-rect', animationIndex)
  })
  
  return (
    <div ref={animation.ref} style={animation.style}>
      <RoundedRect {...props} />
    </div>
  )
}

export function AnimatedPill({ 
  animationPreset = 'horizontal', 
  animationIndex = 0,
  ...props 
}: AnimatedRoundedShapeProps) {
  const animation = useScrollAnimation({
    ...animationPresets[animationPreset],
    randomSeed: generateSeed('pill', animationIndex)
  })
  
  return (
    <div ref={animation.ref} style={animation.style}>
      <Pill {...props} />
    </div>
  )
}

export function AnimatedBlob({ 
  animationPreset = 'dynamic', 
  animationIndex = 0,
  ...props 
}: AnimatedRoundedShapeProps) {
  const animation = useScrollAnimation({
    ...animationPresets[animationPreset],
    randomSeed: generateSeed('blob', animationIndex)
  })
  
  return (
    <div ref={animation.ref} style={animation.style}>
      <Blob {...props} />
    </div>
  )
}

interface AnimatedQuarterCircleProps extends AnimatedRoundedShapeProps {
  corner?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
}

export function AnimatedQuarterCircle({ 
  animationPreset = 'gentle', 
  animationIndex = 0,
  ...props 
}: AnimatedQuarterCircleProps) {
  const animation = useScrollAnimation({
    ...animationPresets[animationPreset],
    randomSeed: generateSeed('quarter-circle', animationIndex)
  })
  
  return (
    <div ref={animation.ref} style={animation.style}>
      <QuarterCircle {...props} />
    </div>
  )
}
