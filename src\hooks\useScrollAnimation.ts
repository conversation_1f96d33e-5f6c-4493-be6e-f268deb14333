'use client'

import { useEffect, useState, useRef } from 'react'

interface ScrollAnimationConfig {
  direction: 'x' | 'y' | 'both'
  intensity: number
  speed: number
  randomSeed?: number
}

export function useScrollAnimation(config: ScrollAnimationConfig) {
  const [scrollY, setScrollY] = useState(0)
  const elementRef = useRef<HTMLDivElement>(null)
  const [randomFactors, setRandomFactors] = useState({ x: 0, y: 0 })

  // Generate consistent random factors based on seed with more varied directions
  useEffect(() => {
    const seed = config.randomSeed || Math.random()
    // Use multiple different seeds to create more varied movement patterns
    const xSeed = seed * 12.9898
    const ySeed = seed * 78.233
    const angleSeed = seed * 45.164

    // Generate base random values
    const baseX = (Math.sin(xSeed) * 43758.5453) % 1
    const baseY = (Math.sin(ySeed) * 43758.5453) % 1
    const angle = (Math.sin(angleSeed) * 43758.5453) % 1

    // Create more dynamic directional patterns
    // Use angle to create diagonal movements and varied directions
    const angleRad = angle * Math.PI * 2 // Full circle
    const magnitude = 0.8 + (Math.abs(baseX) * 0.4) // Vary magnitude between 0.8-1.2

    setRandomFactors({
      x: Math.cos(angleRad) * magnitude,
      y: Math.sin(angleRad) * magnitude
    })
  }, [config.randomSeed])

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY)
    }

    // Use passive listener for better performance
    window.addEventListener('scroll', handleScroll, { passive: true })
    
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  // Calculate transform values
  const getTransform = () => {
    if (!elementRef.current) return 'translate3d(0, 0, 0)'

    const rect = elementRef.current.getBoundingClientRect()
    const elementTop = rect.top + scrollY
    const scrollProgress = (scrollY - elementTop + window.innerHeight) / (window.innerHeight + rect.height)
    
    // Smooth easing function
    const easeInOutQuad = (t: number) => {
      return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t
    }
    
    const easedProgress = easeInOutQuad(Math.max(0, Math.min(1, scrollProgress)))
    
    let translateX = 0
    let translateY = 0
    
    if (config.direction === 'x' || config.direction === 'both') {
      translateX = easedProgress * config.intensity * randomFactors.x * config.speed
    }
    
    if (config.direction === 'y' || config.direction === 'both') {
      translateY = easedProgress * config.intensity * randomFactors.y * config.speed
    }
    
    return `translate3d(${translateX}px, ${translateY}px, 0)`
  }

  return {
    ref: elementRef,
    style: {
      transform: getTransform(),
      transition: 'transform 0.1s ease-out',
      willChange: 'transform'
    }
  }
}

// Predefined animation presets for different shape types with varied movement
export const animationPresets = {
  subtle: {
    direction: 'both' as const,
    intensity: 25,
    speed: 0.6
  },
  gentle: {
    direction: 'both' as const,
    intensity: 35,
    speed: 0.8
  },
  dynamic: {
    direction: 'both' as const,
    intensity: 55,
    speed: 1.2
  },
  flowing: {
    direction: 'both' as const,
    intensity: 40,
    speed: 0.9
  },
  energetic: {
    direction: 'both' as const,
    intensity: 60,
    speed: 1.4
  },
  drift: {
    direction: 'both' as const,
    intensity: 30,
    speed: 0.5
  },
  pulse: {
    direction: 'both' as const,
    intensity: 45,
    speed: 1.1
  },
  float: {
    direction: 'both' as const,
    intensity: 20,
    speed: 0.4
  }
}
